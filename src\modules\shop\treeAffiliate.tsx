import React, { useEffect, useState } from 'react';
import {
    View,
    Text,
    StyleSheet,
    ScrollView,
    TouchableOpacity,
    SafeAreaView
} from 'react-native';
import { Winicon } from 'wini-mobile-components';
import { ColorThemes } from '../../assets/skin/colors';
import { TypoSkin } from '../../assets/skin/typography';
import HeaderShop from '../../components/shop/HeaderShop';
import NavigateShop from '../../components/shop/NavigateShop';
import { useNavigation } from '@react-navigation/native';
import { RootScreen } from '../../router/router';
import { DataController } from '../../base/baseController';
import store from '../../redux/store/store';
import { LoadingUI } from '../../features/loading';
import { Ultis } from '../../utils/Utils';
const TreeAffiliate = () => {
    const [data, setData] = useState<any>();
    const navigation = useNavigation<any>();
    const Customercontroller = new DataController('Customer');
    const Ordercontroller = new DataController('Order');
    const rewardHistorycontroller = new DataController('HistoryReward');

    // Xử lý khi nhấn "Xem tất cả"
    const handleViewAll = () => {
        const customer = store.getState().customer.data;
        navigation.navigate(RootScreen.TreeAffiliateDetail, {
            customerId: customer.Id,
            username: customer.Name
        });
    };
    const [loading, setLoading] = useState(false);
    const fetchData = async () => {
        setLoading(true);

        const customerCurrent = store.getState().customer.data;
        //gọi chung vào promise các api

        const [customerRes, rewardRes] = await Promise.all([
            Customercontroller.getListSimple({
                query: `@ListParent: (*${customerCurrent.Id}*) | @Id: {${customerCurrent.ParentId}} | @Id: {${customerCurrent.Id}}`,
                returns: ['Id', 'Name', 'ParentId', 'ListParent', 'AvatarUrl'],
                sortby: { BY: 'DateCreated', DIRECTION: 'ASC' },
            }),
            rewardHistorycontroller.group({
                reducers: 'LOAD * GROUPBY 1 @CustomerId REDUCE SUM 1 @Value AS TotalReward',
                searchRaw: `@CustomerId: {${customerCurrent.Id}}`,
            }),
        ]);

        if (customerRes.code === 200 && rewardRes.code === 200) {
            var customerIds = customerRes.data.filter((item: any) => item.Id !== customerCurrent.ParentId).map((item: any) => item.Id);
            const orderRes = await Ordercontroller.group({
                reducers: 'LOAD * GROUPBY 1 @CustomerId REDUCE SUM 1 @Value AS TotalPrice',
                searchRaw: `@CustomerId: {${customerIds.join(' | ')}}`,
            });
            var totalprice = orderRes.data.reduce((total: number, item: any) => total + parseFloat(item.TotalPrice), 0);
            var totalreward = rewardRes.data.reduce((total: number, item: any) => total + parseFloat(item.TotalReward), 0);

            // Xây dựng cấu trúc tree đúng
            const buildTreeStructure = (customers: any[]) => {
                // Tạo map để dễ tìm kiếm
                const customerMap = new Map();
                customers.forEach(customer => {
                    customerMap.set(customer.Id, {
                        ...customer,
                        children: [],
                        level: 0,
                        members: customers.filter((a: any) => a.ListParent.includes(customer.Id)).length,
                        revenue: orderRes.data?.filter((a: any) => a.CustomerId === customer.Id).reduce((total: number, item: any) => total + item.TotalPrice, 0) || 0
                    });
                });

                // Tìm root node (không có ParentId hoặc ParentId không có trong danh sách)
                const roots: any[] = [];

                // Xây dựng parent-child relationship
                customers.forEach(customer => {
                    const node = customerMap.get(customer.Id);
                    if (customer.Id === customerCurrent.ParentId ) {
                        // Đây là root node
                        node.level = 0;
                        node.isRoot = true;
                        roots.push(node);
                    } else {
                        // Đây là child node
                        const parent = customerMap.get(customer.ParentId);
                        if (parent) {
                            parent.children.push(node);
                            node.parentId = customer.ParentId;
                            node.isRoot = false;
                        }
                    }
                });

                // Tính level và flatten tree theo thứ tự đúng
                const flattenTree = (nodes: any[], level: number = 0): any[] => {
                    const result: any[] = [];
                    nodes.forEach(node => {
                        node.level = level;
                        result.push({
                            id: node.Id,
                            name: node.Name,
                            isRoot: node.isRoot,
                            subtitle: '(Người giới thiệu)',
                            level: node.level,
                            parentId: node.parentId,
                            members: node.members,
                            revenue: node.revenue,
                            hasChildren: node.children.length > 0
                        });

                        // Thêm children theo thứ tự
                        if (node.children.length > 0) {
                            result.push(...flattenTree(node.children, level + 1));
                        }
                    });
                    return result;
                };

                return flattenTree(roots);
            };

            const treeData = buildTreeStructure(customerRes.data);

            setData({
                stats: {
                    totalMembers: customerRes.data.length,
                    groupRevenue: totalprice,
                    commission: totalreward
                },
                treeData: treeData
            });
        }

        setLoading(false);
    };
    useEffect(() => {
        fetchData();
    }, []);
    // Hàm lấy màu theo level
    const getColorByLevel = (level: number) => {
        switch (level) {
            case 0: return ColorThemes.light.neutral_text_title_color;
            case 1: return ColorThemes.light.primary_main_color;
            case 2: return ColorThemes.light.secondary1_main_color;
            default: return ColorThemes.light.neutral_text_subtitle_color;
        }
    };

    // Component thống kê
    const StatsCard = () => (
        <View style={styles.statsWrapper}>
            {/* Header */}
            <View style={styles.statsHeader}>
                <Text style={styles.statsTitle}>Hệ thống bán hàng</Text>
                <TouchableOpacity onPress={handleViewAll}>
                    <Text style={styles.viewAllText}>Xem tất cả</Text>
                </TouchableOpacity>
            </View>

            {/* Stats Content */}
            <View style={styles.statsContainer}>
                <View style={styles.statItem}>
                    <Text style={styles.statLabel}>Tổng thành viên</Text>
                    <Text style={[styles.statValue, { color: ColorThemes.light.primary_main_color }]}>
                        {data?.stats?.totalMembers ?? 0}
                    </Text>
                </View>
                <View style={styles.statItem}>
                    <Text style={styles.statLabel}>Doanh thu nhóm</Text>
                    <Text style={[styles.statValue, { color: ColorThemes.light.secondary1_main_color }]}>
                        {Ultis.money(data?.stats?.groupRevenue ?? 0)}
                    </Text>
                </View>
                <View style={styles.statItem}>
                    <Text style={styles.statLabel}>Hoa hồng</Text>
                    <Text style={[styles.statValue, { color: ColorThemes.light.secondary3_main_color }]}>
                        {Ultis.money(data?.stats?.commission ?? 0)}
                    </Text>
                </View>
            </View>
        </View>
    );

    // Xử lý khi click vào item để xem chi tiết
    const handleItemClick = (item: any) => {
        navigation.navigate(RootScreen.TreeAffiliateDetail, {
            customerId: item.id,
            username: item.name
        });
    };

    // Component item trong cây
    const TreeItem = ({ item, index }: { item: any; index: number }) => {
        // Kiểm tra xem có phải là item cuối cùng của cùng level không
        const isLastInLevel = index === data?.treeData?.length - 1 ||
            (data?.treeData?.[index + 1]?.level <= item.level);

        // Kiểm tra xem có parent ở level trước không
        const hasParentAbove = index > 0 && data?.treeData?.[index - 1]?.level < item.level;

        return (
            <TouchableOpacity
                style={[styles.treeItem, { marginLeft: item.level * 20 }]}
                onPress={() => handleItemClick(item)}
            >
                {/* Dây nối */}
                {item.level > 0 && (
                    <View style={styles.connectionLines}>
                        {/* Đường dọc từ trên xuống - chỉ hiển thị nếu không phải item cuối */}
                        {!isLastInLevel && (
                            <View style={[styles.verticalLine, { left: item.level * 20 - 10 }]} />
                        )}
                        {/* Đường dọc ngắn từ trên đến ngang */}
                        {hasParentAbove && (
                            <View style={[styles.verticalLineShort, { left: item.level * 20 - 10 }]} />
                        )}
                        {/* Đường ngang */}
                        <View style={[styles.horizontalLine, { left: item.level * 20 - 10 }]} />
                    </View>
                )}

                <View style={styles.treeItemContent}>
                    <View style={styles.treeItemLeft}>
                        <Winicon
                            src="outline/user interface/user"
                            size={16}
                            color={getColorByLevel(item.level)}
                        />
                        <Text style={[styles.treeName, { color: getColorByLevel(item.level) }]}>
                            {item.name}
                            {item.isRoot && (
                                <Text style={styles.subtitle}> {item.subtitle}</Text>
                            )}
                        </Text>
                    </View>
                    {!item.isRoot && (
                        <View style={styles.treeItemRight}>
                            <View style={styles.memberInfo}>
                                <Winicon
                                    src="outline/user interface/users"
                                    size={12}
                                    color={getColorByLevel(item.level)}
                                />
                                <Text style={[styles.memberCount, { color: getColorByLevel(item.level) }]}>
                                    {item.members}
                                </Text>
                            </View>
                            <Text style={[styles.revenue, { color: ColorThemes.light.secondary1_main_color }]}>
                                {item.revenue}
                            </Text>
                        </View>
                    )}
                </View>
            </TouchableOpacity>
        );
    };

    return (
        <SafeAreaView style={styles.container}>
            <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
                {loading ? (
                    <LoadingUI isLoading={loading} />
                ) :<>
                <StatsCard />
                <View style={styles.treeContainer}>
                    {data?.treeData && data.treeData.length > 0 ? (
                        data.treeData.map((item, index) => (
                            <TreeItem key={item.id} item={item} index={index} />
                        ))
                    ) : (
                        <View style={styles.emptyContainer}>
                            <Winicon
                                src="outline/user interface/users"
                                size={48}
                                color={ColorThemes.light.neutral_text_subtitle_color}
                            />
                            <Text style={styles.emptyText}>Không có cấp con</Text>
                            <Text style={styles.emptySubText}>Chưa có thành viên nào trong hệ thống</Text>
                        </View>
                    )}
                </View></>
}
            </ScrollView>
        </SafeAreaView>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: ColorThemes.light.neutral_absolute_background_color,
        marginBottom: 70,
    },
    header: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        maxHeight: 120
    },
    content: {
        flex: 1,
        backgroundColor: ColorThemes.light.neutral_main_background_color,
    },
    statsWrapper: {
        marginHorizontal: 16,
        marginTop: 16,
        backgroundColor: ColorThemes.light.neutral_absolute_background_color,
        borderRadius: 12,
    },
    statsHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingHorizontal: 16,
        paddingTop: 16,
        paddingBottom: 8,
    },
    statsTitle: {
        ...TypoSkin.heading6,
        color: ColorThemes.light.neutral_text_title_color,
        fontWeight: '600',
    },
    viewAllText: {
        ...TypoSkin.regular2,
        color: ColorThemes.light.primary_main_color,
        fontWeight: '500',
    },
    statsContainer: {
        flexDirection: 'row',
        justifyContent: 'space-around',
        paddingVertical: 8,
        paddingHorizontal: 8,
    },
    statItem: {
        alignItems: 'center',
        flex: 1,
    },
    statLabel: {
        ...TypoSkin.regular2,
        color: ColorThemes.light.neutral_text_subtitle_color,
        marginBottom: 8,
        textAlign: 'center',
        fontSize: 12,
    },
    statValue: {
        ...TypoSkin.heading5,
        fontWeight: '700',
    },
    treeContainer: {
        backgroundColor: ColorThemes.light.neutral_absolute_background_color,
        margin: 16,
        borderRadius: 12,
        paddingVertical: 16,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 3,
    },
    treeItem: {
        paddingVertical: 12,
        paddingHorizontal: 16,
        borderBottomWidth: 1,
        borderBottomColor: ColorThemes.light.neutral_lighter_border_color,
    },
    treeItemContent: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
    },
    treeItemLeft: {
        flexDirection: 'row',
        alignItems: 'center',
        flex: 1,
    },
    treeName: {
        ...TypoSkin.medium1,
        marginLeft: 8,
        flex: 1,
    },
    subtitle: {
        ...TypoSkin.regular2,
        color: ColorThemes.light.neutral_text_subtitle_color,
        fontWeight: '400',
    },
    treeItemRight: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 12,
    },
    memberInfo: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 4,
    },
    memberCount: {
        ...TypoSkin.regular2,
        fontWeight: '500',
    },
    revenue: {
        ...TypoSkin.medium1,
        fontWeight: '600',
    },
    // Connection Lines Styles
    connectionLines: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
    },
    verticalLine: {
        position: 'absolute',
        top: 0,
        width: 1,
        height: 56,
        backgroundColor: ColorThemes.light.neutral_lighter_border_color,
    },
    verticalLineShort: {
        position: 'absolute',
        top: 0,
        width: 1,
        height: 24,
        backgroundColor: ColorThemes.light.neutral_lighter_border_color,
    },
    horizontalLine: {
        position: 'absolute',
        top: 24,
        width: 20,
        height: 1,
        backgroundColor: ColorThemes.light.neutral_lighter_border_color,
    },
    // Empty State Styles
    emptyContainer: {
        alignItems: 'center',
        justifyContent: 'center',
        paddingVertical: 40,
        paddingHorizontal: 20,
    },
    emptyText: {
        ...TypoSkin.heading6,
        color: ColorThemes.light.neutral_text_title_color,
        fontWeight: '600',
        marginTop: 16,
        textAlign: 'center',
    },
    emptySubText: {
        ...TypoSkin.regular2,
        color: ColorThemes.light.neutral_text_subtitle_color,
        marginTop: 8,
        textAlign: 'center',
    },
});

export default TreeAffiliate;

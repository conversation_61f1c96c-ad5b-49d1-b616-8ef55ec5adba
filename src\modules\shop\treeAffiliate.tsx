import React, { useState } from 'react';
import {
    View,
    Text,
    StyleSheet,
    ScrollView,
    TouchableOpacity,
    SafeAreaView
} from 'react-native';
import { Winicon } from 'wini-mobile-components';
import { ColorThemes } from '../../assets/skin/colors';
import { TypoSkin } from '../../assets/skin/typography';
import HeaderShop from '../../components/shop/HeaderShop';
import NavigateShop from '../../components/shop/NavigateShop';

// Mock data cho demo
const mockData = {
    stats: {
        totalMembers: 52,
        groupRevenue: '52B',
        commission: '45.1M'
    },
    treeData: [
        {
            id: '1',
            name: '<PERSON><PERSON><PERSON><PERSON>',
            isRoot: true,
            subtitle: '(Người giới thiệu)',
            level: 0
        },
        {
            id: '2',
            name: '<PERSON><PERSON><PERSON><PERSON>',
            members: 200,
            revenue: '500.000.000',
            level: 1
        },
        {
            id: '3',
            name: '<PERSON><PERSON><PERSON><PERSON>',
            members: 200,
            revenue: '500.000',
            level: 2
        },
        {
            id: '4',
            name: '<PERSON><PERSON><PERSON><PERSON>',
            members: 200,
            revenue: '5000 VNĐ',
            level: 2
        },
        {
            id: '5',
            name: '<PERSON>uy<PERSON>n <PERSON>h Tùng',
            members: 200,
            revenue: '500.000.000',
            level: 2
        },
        {
            id: '6',
            name: 'Nguyễn Thanh Tùng',
            members: 200,
            revenue: '500.000.000',
            level: 2
        },
        {
            id: '7',
            name: 'Nguyễn Thanh Tùng',
            members: 200,
            revenue: '500.000.000',
            level: 2
        },
        {
            id: '8',
            name: 'Nguyễn Thanh Tùng',
            members: 200,
            revenue: '200.000',
            level: 2
        },
        {
            id: '9',
            name: 'Nguyễn Thanh Tùng',
            members: 200,
            revenue: '8.000.000',
            level: 2
        }
    ]
};

const TreeAffiliate = () => {
    const [data] = useState(mockData);

    // Hàm lấy màu theo level
    const getColorByLevel = (level: number) => {
        switch (level) {
            case 0: return ColorThemes.light.neutral_text_title_color;
            case 1: return ColorThemes.light.primary_main_color;
            case 2: return ColorThemes.light.secondary1_main_color;
            default: return ColorThemes.light.neutral_text_subtitle_color;
        }
    };

    // Component thống kê
    const StatsCard = () => (
        <View style={styles.statsContainer}>
            
            <View style={styles.statItem}>
                <Text style={styles.statLabel}>Tổng thành viên</Text>
                <Text style={[styles.statValue, { color: ColorThemes.light.primary_main_color }]}>
                    {data.stats.totalMembers}
                </Text>
            </View>
            <View style={styles.statItem}>
                <Text style={styles.statLabel}>Doanh thu nhóm</Text>
                <Text style={[styles.statValue, { color: ColorThemes.light.secondary1_main_color }]}>
                    {data.stats.groupRevenue}
                </Text>
            </View>
            <View style={styles.statItem}>
                <Text style={styles.statLabel}>Hoa hồng</Text>
                <Text style={[styles.statValue, { color: ColorThemes.light.secondary3_main_color }]}>
                    {data.stats.commission}
                </Text>
            </View>
        </View>
    );

    // Component item trong cây
    const TreeItem = ({ item }: { item: any }) => (
        <TouchableOpacity style={[styles.treeItem, { marginLeft: item.level * 20 }]}>
            <View style={styles.treeItemContent}>
                <View style={styles.treeItemLeft}>
                    <Winicon
                        src="outline/user interface/user"
                        size={16}
                        color={getColorByLevel(item.level)}
                    />
                    <Text style={[styles.treeName, { color: getColorByLevel(item.level) }]}>
                        {item.name}
                        {item.isRoot && (
                            <Text style={styles.subtitle}> {item.subtitle}</Text>
                        )}
                    </Text>
                </View>
                {!item.isRoot && (
                    <View style={styles.treeItemRight}>
                        <View style={styles.memberInfo}>
                            <Winicon
                                src="outline/user interface/users"
                                size={12}
                                color={getColorByLevel(item.level)}
                            />
                            <Text style={[styles.memberCount, { color: getColorByLevel(item.level) }]}>
                                {item.members}
                            </Text>
                        </View>
                        <Text style={[styles.revenue, { color: ColorThemes.light.secondary1_main_color }]}>
                            {item.revenue}
                        </Text>
                    </View>
                )}
            </View>
        </TouchableOpacity>
    );

    return (
        <SafeAreaView style={styles.container}>
           

            <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
                <StatsCard />

                <View style={styles.treeContainer}>
                    {data.treeData.map((item) => (
                        <TreeItem key={item.id} item={item} />
                    ))}
                </View>
            </ScrollView>
        </SafeAreaView>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    },
    header: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        maxHeight: 120
    },
    content: {
        flex: 1,
        backgroundColor: ColorThemes.light.neutral_main_background_color,
    },
    statsContainer: {
        flexDirection: 'row',
        justifyContent: 'space-around',
        backgroundColor: ColorThemes.light.neutral_absolute_background_color,
        paddingVertical: 20,
        paddingHorizontal: 16,
        marginHorizontal: 16,
        marginTop: 16,
        borderRadius: 12,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 3,
    },
    statItem: {
        alignItems: 'center',
        flex: 1,
    },
    statLabel: {
        ...TypoSkin.regular2,
        color: ColorThemes.light.neutral_text_subtitle_color,
        marginBottom: 8,
        textAlign: 'center',
    },
    statValue: {
        ...TypoSkin.heading5,
        fontWeight: '700',
    },
    treeContainer: {
        backgroundColor: ColorThemes.light.neutral_absolute_background_color,
        margin: 16,
        borderRadius: 12,
        paddingVertical: 16,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 3,
    },
    treeItem: {
        paddingVertical: 12,
        paddingHorizontal: 16,
        borderBottomWidth: 1,
        borderBottomColor: ColorThemes.light.neutral_lighter_border_color,
    },
    treeItemContent: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
    },
    treeItemLeft: {
        flexDirection: 'row',
        alignItems: 'center',
        flex: 1,
    },
    treeName: {
        ...TypoSkin.medium1,
        marginLeft: 8,
        flex: 1,
    },
    subtitle: {
        ...TypoSkin.regular2,
        color: ColorThemes.light.neutral_text_subtitle_color,
        fontWeight: '400',
    },
    treeItemRight: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 12,
    },
    memberInfo: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 4,
    },
    memberCount: {
        ...TypoSkin.regular2,
        fontWeight: '500',
    },
    revenue: {
        ...TypoSkin.medium1,
        fontWeight: '600',
    },
});

export default TreeAffiliate;

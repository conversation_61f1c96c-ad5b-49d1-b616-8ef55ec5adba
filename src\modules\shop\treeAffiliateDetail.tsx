import React, { useState, useEffect } from 'react';
import {
    View,
    Text,
    StyleSheet,
    ScrollView,
    TouchableOpacity,
    SafeAreaView
} from 'react-native';
import { Winicon } from 'wini-mobile-components';
import { ColorThemes } from '../../assets/skin/colors';
import { TypoSkin } from '../../assets/skin/typography';
import { useNavigation } from '@react-navigation/native';
import NavigateShop from '../../components/shop/NavigateShop';
import HeaderShop from '../../components/shop/HeaderShop';
import { NumberStatusIcon } from '../../Config/Contanst';
import LinearGradient from 'react-native-linear-gradient';

// Import thêm các dependencies cần thiết
import { DataController } from '../../base/baseController';
import store from '../../redux/store/store';
import { LoadingUI } from '../../features/loading';

const TreeAffiliateDetail = () => {
    const [data, setData] = useState<any>();
    const [loading, setLoading] = useState(false);
    const navigation = useNavigation<any>();
    const Customercontroller = new DataController('Customer');
    const Ordercontroller = new DataController('Order');
    const rewardHistorycontroller = new DataController('HistoryReward');

    // Hàm lấy màu theo level
    const getLevelColor = (level: number) => {
        switch (level) {
            case 0: return ColorThemes.light.neutral_text_title_color;
            case 1: return ColorThemes.light.primary_main_color;
            case 2: return ColorThemes.light.secondary1_main_color;
            case 3: return ColorThemes.light.secondary3_main_color;
            default: return ColorThemes.light.neutral_text_subtitle_color;
        }
    };

    // Hàm lấy label level
    const getLevelLabel = (level: number) => {
        switch (level) {
            case 0: return 'ROOT';
            case 1: return 'F1';
            case 2: return 'F2';
            case 3: return 'F3';
            default: return `F${level}`;
        }
    };

    const fetchData = async () => {
        setLoading(true);

        const customer = store.getState().customer.data;

        const [customerRes, rewardRes] = await Promise.all([
            Customercontroller.getListSimple({
                query: `@ListParent: (*${customer.Id}*)`,
                returns: ['Id', 'Name', 'ParentId', 'ListParent', 'AvatarUrl'],
                sortby: { BY: 'DateCreated', DIRECTION: 'ASC' },
            }),
            rewardHistorycontroller.group({
                reducers: 'LOAD * GROUPBY 1 @CustomerId REDUCE SUM 1 @Value AS TotalReward',
                searchRaw: `@CustomerId: {${customer.Id}}`,
            }),
        ]);

        if (customerRes.code === 200 && rewardRes.code === 200) {
            var customerIds = customerRes.data.map((item: any) => item.Id);
            const orderRes = await Ordercontroller.group({
                reducers: 'LOAD * GROUPBY 1 @CustomerId REDUCE SUM 1 @Value AS TotalPrice',
                searchRaw: `@CustomerId: {${customerIds.join(' | ')}}`,
            });

            var totalprice = orderRes.data.reduce((total: number, item: any) => total + parseFloat(item.TotalPrice), 0);
            var totalreward = rewardRes.data.reduce((total: number, item: any) => total + parseFloat(item.TotalReward), 0);

            // Xây dựng cấu trúc tree đúng (tương tự TreeAffiliate)
            const buildTreeStructure = (customers: any[]) => {
                const customerMap = new Map();
                customers.forEach(customer => {
                    customerMap.set(customer.Id, {
                        ...customer,
                        children: [],
                        level: 0,
                        members: customers.filter((a: any) => a.ListParent.includes(customer.Id)).length,
                        revenue: orderRes.data?.filter((a: any) => a.CustomerId === customer.Id).reduce((total: number, item: any) => total + item.TotalPrice, 0) || 0
                    });
                });

                const roots: any[] = [];

                customers.forEach(customer => {
                    const node = customerMap.get(customer.Id);
                    if (!customer.ParentId || !customerMap.has(customer.ParentId)) {
                        node.level = 0;
                        node.isRoot = true;
                        roots.push(node);
                    } else {
                        const parent = customerMap.get(customer.ParentId);
                        if (parent) {
                            parent.children.push(node);
                            node.parentId = customer.ParentId;
                            node.isRoot = false;
                        }
                    }
                });

                const flattenTree = (nodes: any[], level: number = 0): any[] => {
                    const result: any[] = [];
                    nodes.forEach(node => {
                        node.level = level;
                        result.push({
                            id: node.Id,
                            name: node.Name,
                            level: node.level,
                            levelLabel: getLevelLabel(node.level),
                            levelColor: getLevelColor(node.level),
                            parentId: node.parentId,
                            members: node.members,
                            revenue: node.revenue,
                            hasChildren: node.children.length > 0
                        });

                        if (node.children.length > 0) {
                            result.push(...flattenTree(node.children, level + 1));
                        }
                    });
                    return result;
                };

                return flattenTree(roots);
            };

            const affiliateList = buildTreeStructure(customerRes.data);

            setData({
                userInfo: {
                    name: customer.Name,
                    stats: {
                        totalMembers: customerRes.data.length,
                        groupRevenue: totalprice.toLocaleString(),
                        commission: totalreward.toLocaleString()
                    }
                },
                affiliateList: affiliateList
            });
        }

        setLoading(false);
    };

    useEffect(() => {
        fetchData();
    }, []);

    // Component thông tin user và thống kê
    const UserStatsSection = () => (
        <View style={styles.userStatsContainer}>
            <Text style={styles.userName}>{data?.userInfo?.name || 'Loading...'}</Text>

            <View style={styles.statsRow}>
                <View style={styles.statItem}>
                    <Text style={styles.statLabel}>Tổng thành viên</Text>
                    <Text style={[styles.statValue, { color: ColorThemes.light.primary_main_color }]}>
                        {data?.userInfo?.stats?.totalMembers || 0}
                    </Text>
                </View>
                <View style={styles.statItem}>
                    <Text style={styles.statLabel}>Doanh thu nhóm</Text>
                    <Text style={[styles.statValue, { color: ColorThemes.light.secondary1_main_color }]}>
                        {data?.userInfo?.stats?.groupRevenue || 0}
                    </Text>
                </View>
                <View style={styles.statItem}>
                    <Text style={styles.statLabel}>Hoa hồng</Text>
                    <Text style={[styles.statValue, { color: ColorThemes.light.secondary3_main_color }]}>
                        {data?.userInfo?.stats?.commission || 0}
                    </Text>
                </View>
            </View>
        </View>
    );

    // Component item affiliate
    const AffiliateItem = ({ item }: { item: any }) => (
        <LinearGradient
            start={{x: 0, y: 0}}
            end={{x: 1, y: 0}}
            colors={['#90C8FB', '#8DC4F7', '#B6F5FE']}
            style={[styles.affiliateItem, { marginLeft: item.level * 16 }]}
        >
            <TouchableOpacity style={styles.affiliateItem}>
                <View style={styles.affiliateLeft}>
                    <View style={[styles.levelBadge, { backgroundColor: item.levelColor }]}>
                        <Text style={styles.levelText}>{item.levelLabel}</Text>
                    </View>
                    
                    <View style={styles.affiliateInfo}>
                        <View style={styles.nameRow}>
                            <Winicon 
                                src="outline/user interface/user" 
                                size={16} 
                                color={ColorThemes.light.neutral_text_subtitle_color} 
                            />
                            <Text style={styles.affiliateName}>{item.name}</Text>
                        </View>
                        
                        <View style={styles.detailsRow}>
                            <View style={styles.detailItem}>
                                <Winicon 
                                    src="outline/business/money" 
                                    size={14} 
                                    color={ColorThemes.light.neutral_text_subtitle_color} 
                                />
                                <Text style={styles.detailText}>{item.revenue?.toLocaleString() || 0}</Text>
                            </View>
                            
                            <View style={styles.detailItem}>
                                <Winicon 
                                    src="outline/user interface/users" 
                                    size={14} 
                                    color={ColorThemes.light.neutral_text_subtitle_color} 
                                />
                                <Text style={styles.detailText}>{item.members}</Text>
                            </View>
                        </View>
                    </View>
                </View>
                
                {item.hasChildren && (
                    <TouchableOpacity style={styles.expandButton}>
                        <Winicon
                            src="outline/arrows/plus"
                            size={20}
                            color={ColorThemes.light.primary_main_color}
                        />
                    </TouchableOpacity>
                )}
            </TouchableOpacity>
        </LinearGradient>
        
    );

    return (
        <View style={styles.container}>
            {/* Background gradient */}
            

            <SafeAreaView style={styles.safeArea}>
                <HeaderShop />
                 <NavigateShop title="Cây sơ đồ" status={NumberStatusIcon.One} />
                <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
                    <UserStatsSection />
                    {loading ? (
                        <LoadingUI isLoading={loading} />
                    ) : (
                        <View style={styles.affiliateList}>
                            {data?.affiliateList?.map((item: any) => (
                                <AffiliateItem key={item.id} item={item} />
                            ))}
                        </View>
                    )}
                </ScrollView>
            </SafeAreaView>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    },
    backgroundGradient: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        height: 200,
        backgroundColor: ColorThemes.light.primary_main_color,
        borderBottomLeftRadius: 30,
        borderBottomRightRadius: 30,
    },
    safeArea: {
        flex: 1,
    },
    header: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: 16,
        paddingVertical: 12,
        backgroundColor: 'transparent',
    },
    backButton: {
        padding: 8,
    },
    headerCenter: {
        flex: 1,
        alignItems: 'center',
        flexDirection: 'row',
        justifyContent: 'center',
        gap: 8,
    },
    logoContainer: {
        alignItems: 'center',
    },
    logo: {
        width: 32,
        height: 32,
        borderRadius: 16,
        backgroundColor: ColorThemes.light.neutral_absolute_background_color,
        alignItems: 'center',
        justifyContent: 'center',
    },
    logoText: {
        fontSize: 16,
    },
    headerTitle: {
        ...TypoSkin.heading6,
        color: ColorThemes.light.neutral_absolute_background_color,
        fontWeight: '600',
    },
    searchButton: {
        padding: 8,
    },
    content: {
        flex: 1,
        backgroundColor: ColorThemes.light.neutral_main_background_color,
    },
    userStatsContainer: {
        backgroundColor: ColorThemes.light.neutral_absolute_background_color,
        paddingHorizontal: 16,
        paddingVertical: 20,
        marginBottom: 16,
    },
    userName: {
        ...TypoSkin.heading5,
        color: ColorThemes.light.neutral_text_title_color,
        fontWeight: '600',
        marginBottom: 16,
        textAlign: 'left',
    },
    statsRow: {
        flexDirection: 'row',
        justifyContent: 'space-around',
    },
    statItem: {
        alignItems: 'center',
        flex: 1,
    },
    statLabel: {
        ...TypoSkin.regular2,
        color: ColorThemes.light.neutral_text_subtitle_color,
        marginBottom: 8,
        textAlign: 'center',
    },
    statValue: {
        ...TypoSkin.heading5,
        fontWeight: '700',
    },
    affiliateList: {
        paddingHorizontal: 16,
        gap: 12,
    },
    affiliateItem: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        borderRadius: 16,
        paddingHorizontal: 16,
        paddingVertical: 12,
    },
    affiliateLeft: {
        flexDirection: 'row',
        alignItems: 'center',
        flex: 1,
        gap: 12,
    },
    levelBadge: {
        width: 40,
        height: 40,
        borderRadius: 8,
        alignItems: 'center',
        justifyContent: 'center',
    },
    levelText: {
        ...TypoSkin.medium1,
        color: ColorThemes.light.neutral_absolute_background_color,
        fontWeight: '600',
    },
    affiliateInfo: {
        flex: 1,
        gap: 8,
    },
    nameRow: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 8,
    },
    affiliateName: {
        ...TypoSkin.medium1,
        color: ColorThemes.light.neutral_text_title_color,
        fontWeight: '500',
    },
    detailsRow: {
        flexDirection: 'row',
        gap: 16,
    },
    detailItem: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 4,
    },
    detailText: {
        ...TypoSkin.regular2,
        color: ColorThemes.light.neutral_text_subtitle_color,
    },
    expandButton: {
        padding: 8,
    },
});

export default TreeAffiliateDetail;

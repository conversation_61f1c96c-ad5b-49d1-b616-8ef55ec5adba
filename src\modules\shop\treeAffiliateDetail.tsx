import React, { useState } from 'react';
import {
    View,
    Text,
    StyleSheet,
    ScrollView,
    TouchableOpacity,
    SafeAreaView
} from 'react-native';
import { Winicon } from 'wini-mobile-components';
import { ColorThemes } from '../../assets/skin/colors';
import { TypoSkin } from '../../assets/skin/typography';
import { useNavigation } from '@react-navigation/native';
import NavigateShop from '../../components/shop/NavigateShop';
import HeaderShop from '../../components/shop/HeaderShop';
import { NumberStatusIcon } from '../../Config/Contanst';
import LinearGradient from 'react-native-linear-gradient';

// Mock data cho demo
const mockDetailData = {
    userInfo: {
        name: '<PERSON><PERSON><PERSON><PERSON>',
        stats: {
            totalMembers: 52,
            groupRevenue: '52B',
            commission: '45.1M'
        }
    },
    affiliateList: [
        {
            id: '1',
            name: '<PERSON><PERSON><PERSON><PERSON>',
            level: 'F1',
            revenue: '3.000.000',
            members: 100,
            levelColor: ColorThemes.light.primary_main_color
        },
        {
            id: '2',
            name: '<PERSON><PERSON><PERSON><PERSON>',
            level: 'F1',
            revenue: '3.000.000',
            members: 100,
            levelColor: ColorThemes.light.primary_main_color
        },
        {
            id: '3',
            name: 'Nguyễn Thanh Tùng',
            level: 'F1',
            revenue: '3.000.000',
            members: 100,
            levelColor: ColorThemes.light.primary_main_color
        },
        {
            id: '4',
            name: 'Nguyễn Thanh Tùng',
            level: 'F2',
            revenue: '3.000.000',
            members: 2,
            levelColor: ColorThemes.light.secondary1_main_color
        },
        {
            id: '5',
            name: 'Nguyễn Thanh Tùng',
            level: 'F2',
            revenue: '3.000.000',
            members: 2,
            levelColor: ColorThemes.light.secondary1_main_color
        }
    ]
};

const TreeAffiliateDetail = () => {
    const [data] = useState(mockDetailData);
    const navigation = useNavigation<any>();

    // Component thông tin user và thống kê
    const UserStatsSection = () => (
        <View style={styles.userStatsContainer}>
            <Text style={styles.userName}>{data.userInfo.name}</Text>
            
            <View style={styles.statsRow}>
                <View style={styles.statItem}>
                    <Text style={styles.statLabel}>Tổng thành viên</Text>
                    <Text style={[styles.statValue, { color: ColorThemes.light.primary_main_color }]}>
                        {data.userInfo.stats.totalMembers}
                    </Text>
                </View>
                <View style={styles.statItem}>
                    <Text style={styles.statLabel}>Doanh thu nhóm</Text>
                    <Text style={[styles.statValue, { color: ColorThemes.light.secondary1_main_color }]}>
                        {data.userInfo.stats.groupRevenue}
                    </Text>
                </View>
                <View style={styles.statItem}>
                    <Text style={styles.statLabel}>Hoa hồng</Text>
                    <Text style={[styles.statValue, { color: ColorThemes.light.secondary3_main_color }]}>
                        {data.userInfo.stats.commission}
                    </Text>
                </View>
            </View>
        </View>
    );

    // Component item affiliate
    const AffiliateItem = ({ item }: { item: any }) => (
        <LinearGradient
            start={{x: 0, y: 0}}
            end={{x: 1, y: 0}}
            colors={['#90C8FB', '#8DC4F7', '#B6F5FE']}
            style={styles.affiliateItem}
        >
            <TouchableOpacity style={styles.affiliateItem}>
                <View style={styles.affiliateLeft}>
                    <View style={[styles.levelBadge, { backgroundColor: item.levelColor }]}>
                        <Text style={styles.levelText}>{item.level}</Text>
                    </View>
                    
                    <View style={styles.affiliateInfo}>
                        <View style={styles.nameRow}>
                            <Winicon 
                                src="outline/user interface/user" 
                                size={16} 
                                color={ColorThemes.light.neutral_text_subtitle_color} 
                            />
                            <Text style={styles.affiliateName}>{item.name}</Text>
                        </View>
                        
                        <View style={styles.detailsRow}>
                            <View style={styles.detailItem}>
                                <Winicon 
                                    src="outline/business/money" 
                                    size={14} 
                                    color={ColorThemes.light.neutral_text_subtitle_color} 
                                />
                                <Text style={styles.detailText}>{item.revenue}</Text>
                            </View>
                            
                            <View style={styles.detailItem}>
                                <Winicon 
                                    src="outline/user interface/users" 
                                    size={14} 
                                    color={ColorThemes.light.neutral_text_subtitle_color} 
                                />
                                <Text style={styles.detailText}>{item.members}</Text>
                            </View>
                        </View>
                    </View>
                </View>
                
                <TouchableOpacity style={styles.expandButton}>
                    <Winicon 
                        src="outline/arrows/plus" 
                        size={20} 
                        color={ColorThemes.light.primary_main_color} 
                    />
                </TouchableOpacity>
            </TouchableOpacity>
        </LinearGradient>
        
    );

    return (
        <View style={styles.container}>
            {/* Background gradient */}
            

            <SafeAreaView style={styles.safeArea}>
                <HeaderShop />
                 <NavigateShop title="Cây sơ đồ" status={NumberStatusIcon.One} />
                <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
                    <UserStatsSection />
                    <View style={styles.affiliateList}>
                        {data.affiliateList.map((item) => (
                            <AffiliateItem key={item.id} item={item} />
                        ))}
                    </View>
                </ScrollView>
            </SafeAreaView>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    },
    backgroundGradient: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        height: 200,
        backgroundColor: ColorThemes.light.primary_main_color,
        borderBottomLeftRadius: 30,
        borderBottomRightRadius: 30,
    },
    safeArea: {
        flex: 1,
    },
    header: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: 16,
        paddingVertical: 12,
        backgroundColor: 'transparent',
    },
    backButton: {
        padding: 8,
    },
    headerCenter: {
        flex: 1,
        alignItems: 'center',
        flexDirection: 'row',
        justifyContent: 'center',
        gap: 8,
    },
    logoContainer: {
        alignItems: 'center',
    },
    logo: {
        width: 32,
        height: 32,
        borderRadius: 16,
        backgroundColor: ColorThemes.light.neutral_absolute_background_color,
        alignItems: 'center',
        justifyContent: 'center',
    },
    logoText: {
        fontSize: 16,
    },
    headerTitle: {
        ...TypoSkin.heading6,
        color: ColorThemes.light.neutral_absolute_background_color,
        fontWeight: '600',
    },
    searchButton: {
        padding: 8,
    },
    content: {
        flex: 1,
        backgroundColor: ColorThemes.light.neutral_main_background_color,
    },
    userStatsContainer: {
        backgroundColor: ColorThemes.light.neutral_absolute_background_color,
        paddingHorizontal: 16,
        paddingVertical: 20,
        marginBottom: 16,
    },
    userName: {
        ...TypoSkin.heading5,
        color: ColorThemes.light.neutral_text_title_color,
        fontWeight: '600',
        marginBottom: 16,
        textAlign: 'left',
    },
    statsRow: {
        flexDirection: 'row',
        justifyContent: 'space-around',
    },
    statItem: {
        alignItems: 'center',
        flex: 1,
    },
    statLabel: {
        ...TypoSkin.regular2,
        color: ColorThemes.light.neutral_text_subtitle_color,
        marginBottom: 8,
        textAlign: 'center',
    },
    statValue: {
        ...TypoSkin.heading5,
        fontWeight: '700',
    },
    affiliateList: {
        paddingHorizontal: 16,
        gap: 12,
    },
    affiliateItem: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        borderRadius: 16,
        paddingHorizontal: 16,
        paddingVertical: 12,
    },
    affiliateLeft: {
        flexDirection: 'row',
        alignItems: 'center',
        flex: 1,
        gap: 12,
    },
    levelBadge: {
        width: 40,
        height: 40,
        borderRadius: 8,
        alignItems: 'center',
        justifyContent: 'center',
    },
    levelText: {
        ...TypoSkin.medium1,
        color: ColorThemes.light.neutral_absolute_background_color,
        fontWeight: '600',
    },
    affiliateInfo: {
        flex: 1,
        gap: 8,
    },
    nameRow: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 8,
    },
    affiliateName: {
        ...TypoSkin.medium1,
        color: ColorThemes.light.neutral_text_title_color,
        fontWeight: '500',
    },
    detailsRow: {
        flexDirection: 'row',
        gap: 16,
    },
    detailItem: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 4,
    },
    detailText: {
        ...TypoSkin.regular2,
        color: ColorThemes.light.neutral_text_subtitle_color,
    },
    expandButton: {
        padding: 8,
    },
});

export default TreeAffiliateDetail;
